import React, { useState } from "react";
import { FaTimes } from "react-icons/fa";
import { FiCheck, FiX, FiDollarSign, FiCalendar, FiMessageSquare } from "react-icons/fi";
import useModalScrollLock from "../../hooks/useModalScrollLock";
import "../../styles/RequestResponseModal.css";

const RequestResponseModal = ({ isOpen, onClose, request, onSubmit, isSubmitting }) => {
  useModalScrollLock(isOpen);

  const [formData, setFormData] = useState({
    accepted: null,
    paymentType: 'full',
    price: request?.budget || '',
    estimatedDeliveryDate: '',
    message: ''
  });
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || '' : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const handleAcceptanceChange = (accepted) => {
    setFormData(prev => ({
      ...prev,
      accepted,
      // Reset fields when switching between accept/reject
      paymentType: accepted ? prev.paymentType : '',
      price: accepted ? prev.price : '',
      estimatedDeliveryDate: accepted ? prev.estimatedDeliveryDate : ''
    }));
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (formData.accepted === null) {
      newErrors.accepted = "Please choose to accept or reject the request";
    }
    
    if (formData.accepted) {
      if (!formData.price || formData.price <= 0) {
        newErrors.price = "Price must be a positive number";
      }
      
      if (!formData.estimatedDeliveryDate) {
        newErrors.estimatedDeliveryDate = "Estimated delivery date is required";
      } else {
        const selectedDate = new Date(formData.estimatedDeliveryDate);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (selectedDate < today) {
          newErrors.estimatedDeliveryDate = "Delivery date cannot be in the past";
        }
      }
    }
    
    if (!formData.message.trim()) {
      newErrors.message = "Message is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const submitData = {
      accepted: formData.accepted,
      message: formData.message
    };

    if (formData.accepted) {
      submitData.paymentType = formData.paymentType;
      submitData.price = formData.price;
      submitData.estimatedDeliveryDate = formData.estimatedDeliveryDate;
    }

    onSubmit(submitData);
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen || !request) return null;

  return (
    <div className="request-response-modal-overlay" onClick={handleOverlayClick}>
      <div className="request-response-modal">
        <div className="modal-header">
          <h2 className="modal-title">Respond to Custom Request</h2>
          <button className="modal-close" onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        <div className="modal-content">
          {/* Request Summary */}
          <div className="request-summary">
            <h3>Request Details</h3>
            <div className="summary-grid">
              <div className="summary-item">
                <label>Title:</label>
                <span>{request.title}</span>
              </div>
              <div className="summary-item">
                <label>Buyer:</label>
                <span>{request.buyer?.firstName} {request.buyer?.lastName}</span>
              </div>
              <div className="summary-item">
                <label>Budget:</label>
                <span>${request.budget}</span>
              </div>
              <div className="summary-item">
                <label>Content Type:</label>
                <span>{request.contentType}</span>
              </div>
            </div>
            <div className="request-description">
              <label>Description:</label>
              <p>{request.description}</p>
            </div>
          </div>

          {/* Response Form */}
          <form onSubmit={handleSubmit} className="response-form">
            {/* Accept/Reject Decision */}
            <div className="form-group decision-group">
              <label className="form-label">Your Decision *</label>
              <div className="decision-buttons">
                <button
                  type="button"
                  className={`decision-btn accept-btn ${formData.accepted === true ? 'active' : ''}`}
                  onClick={() => handleAcceptanceChange(true)}
                >
                  <FiCheck />
                  Accept Request
                </button>
                <button
                  type="button"
                  className={`decision-btn reject-btn ${formData.accepted === false ? 'active' : ''}`}
                  onClick={() => handleAcceptanceChange(false)}
                >
                  <FiX />
                  Reject Request
                </button>
              </div>
              {errors.accepted && <span className="error-message">{errors.accepted}</span>}
            </div>

            {/* Acceptance Details */}
            {formData.accepted === true && (
              <div className="acceptance-details">
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="paymentType" className="form-label">
                      <FiDollarSign />
                      Payment Type *
                    </label>
                    <select
                      id="paymentType"
                      name="paymentType"
                      value={formData.paymentType}
                      onChange={handleInputChange}
                      className="form-select"
                    >
                      <option value="full">Full Payment Upfront</option>
                      <option value="half">Half Payment Upfront</option>
                    </select>
                    <small className="form-help">
                      {formData.paymentType === 'half' 
                        ? 'Buyer pays 50% now, remaining 50% after completion'
                        : 'Buyer pays full amount upfront'
                      }
                    </small>
                  </div>

                  <div className="form-group">
                    <label htmlFor="price" className="form-label">
                      <FiDollarSign />
                      Your Price (USD) *
                    </label>
                    <input
                      type="number"
                      id="price"
                      name="price"
                      value={formData.price}
                      onChange={handleInputChange}
                      className={`form-input ${errors.price ? 'error' : ''}`}
                      placeholder="0.00"
                      min="0.01"
                      step="0.01"
                      required
                    />
                    {errors.price && <span className="error-message">{errors.price}</span>}
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="estimatedDeliveryDate" className="form-label">
                    <FiCalendar />
                    Estimated Delivery Date *
                  </label>
                  <input
                    type="date"
                    id="estimatedDeliveryDate"
                    name="estimatedDeliveryDate"
                    value={formData.estimatedDeliveryDate}
                    onChange={handleInputChange}
                    className={`form-input ${errors.estimatedDeliveryDate ? 'error' : ''}`}
                    min={new Date().toISOString().split('T')[0]}
                    required
                  />
                  {errors.estimatedDeliveryDate && <span className="error-message">{errors.estimatedDeliveryDate}</span>}
                </div>
              </div>
            )}

            {/* Message */}
            <div className="form-group">
              <label htmlFor="message" className="form-label">
                <FiMessageSquare />
                {formData.accepted === true ? 'Message to Buyer *' : formData.accepted === false ? 'Reason for Rejection *' : 'Message *'}
              </label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                className={`form-textarea ${errors.message ? 'error' : ''}`}
                placeholder={
                  formData.accepted === true 
                    ? "Thank you for your request. I'm excited to work on this project..."
                    : formData.accepted === false
                    ? "Thank you for your interest. Unfortunately, I cannot take on this project because..."
                    : "Your message to the buyer..."
                }
                rows="4"
                required
              />
              {errors.message && <span className="error-message">{errors.message}</span>}
            </div>

            {/* Submit Button */}
            <div className="form-actions">
              <button
                type="button"
                className="btn-secondary"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className={`btn-primary ${formData.accepted === true ? 'accept' : formData.accepted === false ? 'reject' : ''}`}
                disabled={isSubmitting}
              >
                {isSubmitting 
                  ? 'Submitting...' 
                  : formData.accepted === true 
                    ? 'Accept & Send Response'
                    : formData.accepted === false
                    ? 'Reject & Send Response'
                    : 'Send Response'
                }
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default RequestResponseModal;
