const ErrorResponse = require("../utils/errorResponse");
const Payment = require("../models/Payment");
const Order = require("../models/Order");
const User = require("../models/User");
const Card = require("../models/Card");
const CustomRequest = require("../models/CustomRequest");
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
const { validationResult } = require("express-validator");

// Helper function to update custom request payment status
const updateCustomRequestPaymentStatus = async (order) => {
  try {
    const customRequest = await CustomRequest.findById(order.customRequestId);
    if (!customRequest) {
      console.error(`Custom request not found: ${order.customRequestId}`);
      return;
    }

    // Determine if this is initial or final payment
    const isInitialPayment = customRequest.paymentDetails.initialOrderId &&
                            customRequest.paymentDetails.initialOrderId.toString() === order._id.toString();
    const isFinalPayment = customRequest.paymentDetails.finalOrderId &&
                          customRequest.paymentDetails.finalOrderId.toString() === order._id.toString();

    if (isInitialPayment) {
      // Update initial payment status
      customRequest.paymentDetails.initialPaymentCompleted = true;
      customRequest.paymentDetails.paidAmount += order.amount;

      // Update status based on payment type
      if (customRequest.sellerResponse.paymentType === 'full') {
        customRequest.paymentDetails.finalPaymentCompleted = true;
        customRequest.status = 'In Progress';
      } else {
        customRequest.status = 'In Progress';
      }
    } else if (isFinalPayment) {
      // Update final payment status
      customRequest.paymentDetails.finalPaymentCompleted = true;
      customRequest.paymentDetails.paidAmount += order.amount;
      customRequest.paymentDetails.remainingAmount = 0;

      // If content is already submitted, mark as completed
      if (customRequest.contentSubmission.isSubmitted) {
        customRequest.status = 'Completed';
      }
    }

    await customRequest.save();
    console.log(`Updated custom request payment status: ${customRequest._id}`);
  } catch (error) {
    console.error('Error updating custom request payment status:', error);
  }
};

// Helper function to extract and save card details from payment intent
const extractAndSaveCardDetails = async (paymentIntent, userId, saveCard = true) => {
  try {
    // Get the payment method from Stripe
    const paymentMethod = await stripe.paymentMethods.retrieve(
      paymentIntent.payment_method
    );

    if (!paymentMethod || !paymentMethod.card) {
      return null;
    }

    const cardDetails = {
      cardType: paymentMethod.card.brand,
      lastFourDigits: paymentMethod.card.last4,
    };

    // Check if this card already exists for the user
    const existingCard = await Card.findOne({
      user: userId,
      fingerprint: paymentMethod.card.fingerprint,
      isActive: true,
    });

    if (existingCard) {
      // Card already exists, return reference to it
      cardDetails.cardId = existingCard._id;
      return cardDetails;
    }

    // Only save card if user explicitly wants to save it
    if (!saveCard) {
      console.log('User chose not to save card, returning basic card details only');
      return cardDetails;
    }

    // Save card as requested by user
    try {
      // Get or create Stripe customer for user
      const user = await User.findById(userId);
      let stripeCustomerId = user.paymentInfo?.stripeCustomerId;

      if (!stripeCustomerId) {
        const customer = await stripe.customers.create({
          email: user.email,
          name: `${user.firstName} ${user.lastName}`,
          metadata: {
            userId: user.id,
          },
        });

        stripeCustomerId = customer.id;

        // Update user with Stripe customer ID
        await User.findByIdAndUpdate(userId, {
          "paymentInfo.stripeCustomerId": stripeCustomerId,
        });
      }

      // Attach payment method to customer if not already attached
      if (!paymentMethod.customer) {
        try {
          await stripe.paymentMethods.attach(paymentMethod.id, {
            customer: stripeCustomerId,
          });
          console.log(`Successfully attached payment method ${paymentMethod.id} to customer ${stripeCustomerId}`);
        } catch (attachError) {
          console.error("Error attaching payment method to customer:", attachError);

          // If payment method was already used and can't be attached,
          // we can still save the card details for reference
          if (attachError.code === 'resource_missing' ||
            attachError.message?.includes('previously used') ||
            attachError.message?.includes('detached')) {
            console.log("Payment method cannot be reused, but saving card details for reference");
          } else {
            // For other errors, re-throw
            throw attachError;
          }
        }
      }

      // Check if this is the user's first card to make it default
      const userCardCount = await Card.countDocuments({
        user: userId,
        isActive: true,
      });
      const isDefault = userCardCount === 0;

      // Create card record - only save if payment method is properly attached or if it's for reference
      const cardData = {
        user: userId,
        stripePaymentMethodId: paymentMethod.id,
        lastFourDigits: paymentMethod.card.last4,
        cardType: paymentMethod.card.brand,
        expiryMonth: paymentMethod.card.exp_month,
        expiryYear: paymentMethod.card.exp_year,
        cardholderName:
          paymentMethod.billing_details.name ||
          `${user.firstName} ${user.lastName}`,
        fingerprint: paymentMethod.card.fingerprint,
        isDefault,
        billingAddress: {
          line1: paymentMethod.billing_details.address?.line1,
          line2: paymentMethod.billing_details.address?.line2,
          city: paymentMethod.billing_details.address?.city,
          state: paymentMethod.billing_details.address?.state,
          postalCode: paymentMethod.billing_details.address?.postal_code,
          country: paymentMethod.billing_details.address?.country,
        },
      };

      // If payment method is attached to customer, mark as reusable
      if (paymentMethod.customer) {
        cardData.isReusable = true;
      } else {
        // If not attached (due to previous use), mark as non-reusable but keep for reference
        cardData.isReusable = false;
        cardData.isDefault = false; // Don't make non-reusable cards default
      }

      const newCard = await Card.create(cardData);

      cardDetails.cardId = newCard._id;
      return cardDetails;
    } catch (cardSaveError) {
      console.error("Error saving card details:", cardSaveError);
      // Return basic card details even if saving fails
      return cardDetails;
    }
  } catch (error) {
    console.error("Error extracting card details:", error);
    return null;
  }
};

// @desc    Get all payments
// @route   GET /api/payments
// @access  Private/Admin
exports.getPayments = async (req, res, next) => {
  try {
    const payments = await Payment.find()
      .populate({
        path: "buyer",
        select: "firstName lastName email",
      })
      .populate({
        path: "seller",
        select: "firstName lastName email",
      })
      .populate("order")
      .sort("-createdAt");

    res.status(200).json({
      success: true,
      count: payments.length,
      data: payments,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single payment
// @route   GET /api/payments/:id
// @access  Private
exports.getPayment = async (req, res, next) => {
  try {
    const payment = await Payment.findById(req.params.id)
      .populate({
        path: "buyer",
        select: "firstName lastName email",
      })
      .populate({
        path: "seller",
        select: "firstName lastName email",
      })
      .populate("order");

    if (!payment) {
      return next(
        new ErrorResponse(`Payment not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is payment buyer or seller or admin
    if (
      payment.buyer._id.toString() !== req.user.id &&
      payment.seller._id.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to view this payment`,
          403
        )
      );
    }

    res.status(200).json({
      success: true,
      data: payment,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create payment intent
// @route   POST /api/payments/create-intent
// @access  Private/Buyer
exports.createPaymentIntent = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { orderId } = req.body;

    // Get order with seller information
    const order = await Order.findById(orderId)
      .populate("content")
      .populate("seller", "paymentInfo firstName lastName");

    if (!order) {
      return next(
        new ErrorResponse(`Order not found with id of ${orderId}`, 404)
      );
    }

    // Make sure user is order buyer
    if (order.buyer.toString() !== req.user.id && req.user.role !== "admin") {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to pay for this order`,
          403
        )
      );
    }

    // Check if order is already paid
    if (order.paymentStatus === "Completed") {
      console.log(`Attempt to create payment intent for already completed order: ${orderId}, status: ${order.paymentStatus}`);

      // Get existing payment record for this order
      const existingPayment = await Payment.findOne({ order: orderId });

      // Instead of throwing an error, return a success response indicating the order is already paid
      // This prevents frontend errors and provides better UX
      return res.status(200).json({
        success: true,
        message: "Order is already paid and completed",
        orderStatus: "completed",
        paymentStatus: "completed",
        alreadyCompleted: true, // Flag to help frontend handle this case
        data: {
          order: {
            _id: order._id,
            paymentStatus: order.paymentStatus,
            status: order.status,
            paymentIntentId: order.paymentIntentId,
            amount: order.amount,
            totalAmount: order.totalAmount
          },
          payment: existingPayment
        }
      });
    }

    // Check if there's already an active payment intent for this order
    if (order.paymentIntentId) {
      try {
        const existingPaymentIntent = await stripe.paymentIntents.retrieve(order.paymentIntentId);

        // If payment intent exists and is in a usable state, return it
        if (existingPaymentIntent &&
          (existingPaymentIntent.status === 'requires_payment_method' ||
            existingPaymentIntent.status === 'requires_confirmation' ||
            existingPaymentIntent.status === 'requires_action')) {

          return res.status(200).json({
            success: true,
            clientSecret: existingPaymentIntent.client_secret,
            paymentIntentId: existingPaymentIntent.id,
          });
        }

        // If payment intent succeeded, update order status if not already completed
        if (existingPaymentIntent.status === 'succeeded') {
          if (order.paymentStatus !== 'Completed') {
            order.paymentStatus = 'Completed';
            order.status = 'Completed';
            await order.save();
          }

          // Get existing payment record for this order
          const existingPayment = await Payment.findOne({ order: orderId });

          // Return success response instead of error for already paid orders
          return res.status(200).json({
            success: true,
            message: "Order is already paid and completed",
            orderStatus: "completed",
            paymentStatus: "completed",
            alreadyCompleted: true, // Flag to help frontend handle this case
            data: {
              order: {
                _id: order._id,
                paymentStatus: order.paymentStatus,
                status: order.status,
                paymentIntentId: order.paymentIntentId,
                amount: order.amount,
                totalAmount: order.totalAmount
              },
              payment: existingPayment
            }
          });
        }

        // If payment intent failed or was canceled, we'll create a new one
        console.log(`Previous payment intent ${order.paymentIntentId} status: ${existingPaymentIntent.status}. Creating new payment intent.`);

      } catch (stripeError) {
        // If payment intent doesn't exist in Stripe, we'll create a new one
        console.log(`Payment intent ${order.paymentIntentId} not found in Stripe. Creating new payment intent.`);
      }
    }

    // Check if payment deadline has expired
    if (order.paymentDeadline && new Date() > order.paymentDeadline) {
      return next(
        new ErrorResponse(
          `Payment deadline has expired. This order can no longer be paid.`,
          400
        )
      );
    }

    // Check if seller has completed Stripe Connect onboarding
    const seller = order.seller;
    if (!seller.paymentInfo?.stripeConnectId) {
      return next(
        new ErrorResponse(
          `Seller has not completed payment setup. Please contact seller to complete their payment onboarding.`,
          400
        )
      );
    }

    // Verify seller's Stripe Connect account is active
    try {
      const connectAccount = await stripe.accounts.retrieve(
        seller.paymentInfo.stripeConnectId
      );
      if (
        !connectAccount.details_submitted ||
        !connectAccount.charges_enabled
      ) {
        return next(
          new ErrorResponse(
            `Seller's payment account is not fully set up. Please contact seller.`,
            400
          )
        );
      }
    } catch (stripeError) {
      return next(
        new ErrorResponse(
          `Seller's payment account is invalid. Please contact seller.`,
          400
        )
      );
    }

    // Get or create Stripe customer for user
    let stripeCustomerId = req.user.paymentInfo?.stripeCustomerId;

    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        email: req.user.email,
        name: `${req.user.firstName} ${req.user.lastName}`,
        metadata: {
          userId: req.user.id,
        },
      });

      stripeCustomerId = customer.id;

      // Update user with Stripe customer ID
      await User.findByIdAndUpdate(req.user.id, {
        "paymentInfo.stripeCustomerId": stripeCustomerId,
      });
    }

    // Calculate platform fee and seller amount using database settings
    const Setting = require("../models/Setting");
    const settings = await Setting.getSingleton();
    const totalAmount = order.amount;

    // Use the new fee calculation method for transparency
    const feeBreakdown = Setting.calculateFeeBreakdown(totalAmount, settings);
    const platformFeeAmount = Math.round(feeBreakdown.platformCommission * 100); // Platform fee in cents
    const transferAmount = Math.round(feeBreakdown.sellerEarningsBeforeStripeFees * 100); // Amount to transfer (Stripe will deduct their fees from this)

    // Create payment intent with Stripe Connect transfer
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(totalAmount * 100), // Total amount in cents
      currency: "usd",
      customer: stripeCustomerId,
      description: `Payment for ${order.content.title || "Digital Content"
        } - Order #${order._id}`,
      metadata: {
        orderId: order._id.toString(),
        contentId: order.content._id.toString(),
        buyerId: req.user.id,
        sellerId: order.seller._id.toString(),
        orderType: order.orderType,
        platformFee: (platformFeeAmount / 100).toString(),
        transferAmount: (transferAmount / 100).toString(),
        stripeFee: feeBreakdown.stripeFee.toString(),
        finalSellerEarnings: feeBreakdown.finalSellerEarnings.toString(),
        buyerPaysAmount: feeBreakdown.buyerPaysAmount.toString(),
      },
      // Stripe Connect configuration for automatic transfers
      transfer_data: {
        destination: seller.paymentInfo.stripeConnectId,
      },
      application_fee_amount: platformFeeAmount, // Platform commission from database settings
      // Allow payment method to be saved for future use
      setup_future_usage: 'off_session',
      // Required for Indian regulations
      shipping: {
        name: req.user.name || "Digital Content Buyer",
        address: {
          line1: "Digital Content Delivery",
          city: "Online",
          state: "Digital",
          postal_code: "000000",
          country: "IN",
        },
      },
    });

    // Update order with new payment intent ID
    await Order.findByIdAndUpdate(orderId, {
      paymentIntentId: paymentIntent.id,
    });

    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Confirm payment
// @route   POST /api/payments/confirm
// @access  Private/Buyer
exports.confirmPayment = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { paymentIntentId, orderId, saveCard = false } = req.body;

    // Get order
    const order = await Order.findById(orderId)
      .populate("buyer", "firstName lastName email mobile")
      .populate("content", "title sport contentType");

    if (!order) {
      return next(
        new ErrorResponse(`Order not found with id of ${orderId}`, 404)
      );
    }

    // Make sure user is order buyer
    if (
      order.buyer._id.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to confirm payment for this order`,
          403
        )
      );
    }

    // Check if order is already completed
    if (order.paymentStatus === "Completed") {
      console.log(`Attempt to confirm payment for already completed order: ${orderId}, status: ${order.paymentStatus}`);
      // Order already completed, return existing payment record
      const existingPayment = await Payment.findOne({ order: orderId });
      return res.status(200).json({
        success: true,
        message: "Payment already confirmed",
        data: {
          order,
          payment: existingPayment
        }
      });
    }

    // Get payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== "succeeded") {
      return next(new ErrorResponse(`Payment has not been completed`, 400));
    }

    // Extract and save card details (only if user wants to save the card)
    const cardDetails = await extractAndSaveCardDetails(
      paymentIntent,
      req.user.id,
      saveCard
    );

    // Update order
    order.paymentStatus = "Completed";
    order.paymentIntentId = paymentIntentId;
    order.status = "Completed";

    // Add card details to order if available
    if (cardDetails) {
      order.cardDetails = {
        cardType: cardDetails.cardType,
        lastFourDigits: cardDetails.lastFourDigits,
      };
    }

    await order.save();

    // Check if payment record already exists
    let payment = await Payment.findOne({ paymentIntentId });

    if (!payment) {
      // Create payment record with card details
      const paymentData = {
        order: orderId,
        buyer: order.buyer._id,
        seller: order.seller,
        amount: order.amount,
        platformFee: order.platformFee,
        sellerEarnings: order.sellerEarnings,
        totalAmount: order.totalAmount || order.amount, // Buyer pays exactly the listed amount
        paymentMethod: "card",
        paymentIntentId,
        status: "Completed",
        payoutStatus: "Pending",
      };

      // Add card details if available
      if (cardDetails) {
        paymentData.cardDetails = cardDetails;
      }

      payment = await Payment.create(paymentData);
    }

    // Mark content as sold and update auction status if applicable
    if (order.content) {
      const Content = require("../models/Content");
      const content = await Content.findById(order.content);

      if (content && content.saleType === "Auction") {
        await Content.findByIdAndUpdate(order.content, {
          isSold: true,
          soldAt: new Date(),
          auctionStatus: "Ended",
          auctionEndedAt: new Date(),
        });
      }
    }

    // Update custom request payment details if this is a custom order
    if (order.customRequestId) {
      await updateCustomRequestPaymentStatus(order);
    }

    // Send order receipt email in background (don't await)
    const { orderReceiptTemplate } = require("../utils/emailTemplates");
    const sendEmail = require("../utils/sendEmail");

    const emailData = orderReceiptTemplate({
      order,
      buyer: order.buyer,
      content: order.content,
      cardDetails: order.cardDetails,
    });

    sendEmail({
      email: order.buyer.email,
      subject: emailData.subject,
      message: emailData.message,
      html: emailData.html,
    }).then(() => {
      console.log(`Order receipt email sent to ${order.buyer.email}`);
    }).catch((emailError) => {
      console.error("Error sending order receipt email:", emailError);
      // Email failure doesn't affect payment confirmation
    });

    res.status(200).json({
      success: true,
      data: payment,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Process Stripe webhook
// @route   POST /api/payments/webhook
// @access  Public
exports.webhook = async (req, res, next) => {
  try {
    let event;

    if (process.env.NODE_ENV === "development") {
      // 🔓 Bypass signature verification in development
      event = req.body;
    } else {
      // 🔐 Verify signature in production
      const sig = req.headers["stripe-signature"];

      if (!sig) {
        console.error('Missing Stripe signature header');
        return res.status(400).send('Missing Stripe signature');
      }

      if (!process.env.STRIPE_WEBHOOK_SECRET) {
        console.error('Missing STRIPE_WEBHOOK_SECRET environment variable');
        return res.status(500).send('Webhook configuration error');
      }

      try {
        event = stripe.webhooks.constructEvent(
          req.body,
          sig,
          process.env.STRIPE_WEBHOOK_SECRET
        );
      } catch (err) {
        console.error('Webhook signature verification failed:', err.message);
        return res.status(400).send(`Webhook Error: ${err.message}`);
      }
    }

    // Handle the event
    if (event.type === "payment_intent.succeeded") {
      const paymentIntent = event.data.object;
      const orderId = paymentIntent.metadata?.orderId;

      if (!orderId) {
        return res.status(200).send('OK - No order ID in metadata');
      }

      const order = await Order.findById(orderId)
        .populate("buyer", "firstName lastName email mobile")
        .populate("content", "title sport contentType");

      if (!order) {
        return res.status(200).send('OK - Order not found');
      }

      if (order.paymentStatus === "Completed") {
        return res.status(200).send('OK - Order already completed');
      }

      // Extract and save card details (default to true for webhook payments)
      const cardDetails = await extractAndSaveCardDetails(
        paymentIntent,
        order.buyer._id,
        true
      );

      order.paymentStatus = "Completed";
      order.paymentIntentId = paymentIntent.id;
      order.status = "Completed";

      // Add card details to order if available
      if (cardDetails) {
        order.cardDetails = {
          cardType: cardDetails.cardType,
          lastFourDigits: cardDetails.lastFourDigits,
        };
      }

      await order.save();

      // Mark content as sold and update auction status if applicable
      if (order.content) {
        const Content = require("../models/Content");
        const content = await Content.findById(order.content);

        if (content && content.saleType === "Auction") {
          await Content.findByIdAndUpdate(order.content, {
            isSold: true,
            soldAt: new Date(),
            auctionStatus: "Ended",
            auctionEndedAt: new Date(),
          });
        }
      }

      // Update custom request payment details if this is a custom order
      if (order.customRequestId) {
        await updateCustomRequestPaymentStatus(order);
      }

      const existingPayment = await Payment.findOne({
        paymentIntentId: paymentIntent.id,
      });

      if (!existingPayment) {
        // Extract fee breakdown from payment intent metadata
        const platformFee = parseFloat(paymentIntent.metadata?.platformFee || order.platformFee);
        const transferAmount = parseFloat(paymentIntent.metadata?.transferAmount || (order.amount - platformFee));
        const stripeFee = parseFloat(paymentIntent.metadata?.stripeFee || 0);
        const finalSellerEarnings = parseFloat(paymentIntent.metadata?.finalSellerEarnings || (transferAmount - stripeFee));

        // Store comprehensive fee breakdown for transparency

        // Create payment record with card details (reuse the same cardDetails)
        const paymentData = {
          order: orderId,
          buyer: order.buyer._id,
          seller: order.seller,
          amount: order.amount,
          platformFee: platformFee,
          stripeProcessingFee: stripeFee, // Store Stripe fee for transparency
          sellerEarnings: finalSellerEarnings, // Final amount seller receives after all fees
          totalAmount: order.totalAmount || order.amount, // Buyer pays exactly the listed amount
          paymentMethod: "card",
          paymentIntentId: paymentIntent.id,
          status: "Completed",
          payoutStatus: "Pending",
        };

        // Add card details if available
        if (cardDetails) {
          paymentData.cardDetails = cardDetails;
        }

        await Payment.create(paymentData);

        // Send order receipt email in background (don't await)
        const {
          orderReceiptTemplate,
        } = require("../utils/emailTemplates");
        const sendEmail = require("../utils/sendEmail");

        const emailData = orderReceiptTemplate({
          order,
          buyer: order.buyer,
          content: order.content,
          cardDetails: order.cardDetails,
        });

        sendEmail({
          email: order.buyer.email,
          subject: emailData.subject,
          message: emailData.message,
          html: emailData.html,
        }).then(() => {
          console.log(`Order receipt email sent to ${order.buyer.email} via webhook`);
        }).catch((emailError) => {
          console.error("Error sending order receipt email from webhook:", emailError);
          // Email failure doesn't affect webhook handling
        });
      }
    }

    // Always return 200 for successful webhook processing
    return res.status(200).send('OK');

  } catch (err) {
    console.error('Webhook processing error:', err);

    // For webhook errors, we should return 200 to prevent Stripe from retrying
    // unless it's a critical error that should be retried
    if (err.name === 'ValidationError' || err.name === 'CastError') {
      return res.status(200).send('OK - Validation error handled');
    }

    // For other errors, return 500 so Stripe will retry
    return res.status(500).send('Internal server error');
  }
};

// @desc    Get buyer payments
// @route   GET /api/payments/buyer
// @access  Private/Buyer
exports.getBuyerPayments = async (req, res, next) => {
  try {
    const payments = await Payment.find({ buyer: req.user.id })
      .populate("order")
      .sort("-createdAt");

    res.status(200).json({
      success: true,
      count: payments.length,
      data: payments,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller payments
// @route   GET /api/payments/seller
// @access  Private/Seller
exports.getSellerPayments = async (req, res, next) => {
  try {
    const payments = await Payment.find({ seller: req.user.id })
      .populate("order")
      .sort("-createdAt");

    res.status(200).json({
      success: true,
      count: payments.length,
      data: payments,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Process payout
// @route   POST /api/payments/:id/payout
// @access  Private/Admin
exports.processPayout = async (req, res, next) => {
  try {
    const payment = await Payment.findById(req.params.id).populate(
      "seller",
      "paymentInfo firstName lastName email"
    );

    if (!payment) {
      return next(
        new ErrorResponse(`Payment not found with id of ${req.params.id}`, 404)
      );
    }

    if (payment.payoutStatus !== "Pending") {
      return next(
        new ErrorResponse(`Payment payout has already been processed`, 400)
      );
    }

    const seller = payment.seller;

    // Check if seller has Stripe Connect ID
    if (!seller.paymentInfo || !seller.paymentInfo.stripeConnectId) {
      return next(
        new ErrorResponse(`Seller does not have a Stripe Connect account`, 400)
      );
    }

    try {
      // Process payout through Stripe
      const payout = await stripe.transfers.create({
        amount: Math.round(payment.sellerEarnings * 100), // Convert to cents
        currency: "usd",
        destination: seller.paymentInfo.stripeConnectId,
        description: `Payout for order ${payment.order}`,
        metadata: {
          paymentId: payment._id.toString(),
          orderId: payment.order.toString(),
          sellerId: seller._id.toString(),
        },
      });

      // Update payment
      payment.payoutStatus = "Completed";
      payment.payoutId = payout.id;
      payment.payoutDate = new Date();
      await payment.save();

      res.status(200).json({
        success: true,
        data: payment,
        payout,
      });
    } catch (stripeError) {
      console.error("Stripe payout error:", stripeError);

      // Update payment status to failed
      payment.payoutStatus = "Failed";
      await payment.save();

      return next(
        new ErrorResponse(`Payout failed: ${stripeError.message}`, 400)
      );
    }
  } catch (err) {
    next(err);
  }
};

// @desc    Create Stripe Connect account
// @route   POST /api/payments/create-connect-account
// @access  Private/Seller
exports.createConnectAccount = async (req, res, next) => {
  try {
    const { email, firstName, lastName } = req.body;
    const userId = req.user.id;

    // Check if user already has a Stripe Connect account
    const user = await User.findById(userId);
    let accountId = user.paymentInfo?.stripeConnectId;

    if (accountId) {
      // User already has an account, create account link for completion
      try {
        // Verify the account still exists in Stripe
        const existingAccount = await stripe.accounts.retrieve(accountId);

        // Check if this is for onboarding or settings
        const isOnboarding = req.body.context === "onboarding";
        const baseUrl = isOnboarding
          ? "/stripe-onboarding-return"
          : "/seller/payment-settings";

        // Create account link for existing account to complete onboarding
        const accountLink = await stripe.accountLinks.create({
          account: accountId,
          refresh_url: `${process.env.FRONTEND_URL
            }${baseUrl}?stripe_refresh=true&account_id=${accountId}${isOnboarding ? "&context=onboarding" : ""
            }`,
          return_url: `${process.env.FRONTEND_URL
            }${baseUrl}?stripe_success=true&account_id=${accountId}${isOnboarding ? "&context=onboarding" : ""
            }`,
          type: "account_onboarding",
          collect: "eventually_due",
        });

        return res.status(200).json({
          success: true,
          data: {
            accountId: accountId,
            onboardingUrl: accountLink.url,
            accountType: existingAccount.type || "express",
            isExisting: true,
          },
        });
      } catch (stripeError) {
        // If account doesn't exist in Stripe anymore, clear it and create new one
        if (stripeError.code === "resource_missing") {
          await User.findByIdAndUpdate(userId, {
            $unset: { "paymentInfo.stripeConnectId": 1 },
          });
          accountId = null;
        } else {
          throw stripeError;
        }
      }
    }

    // Create new Stripe Express account
    const account = await stripe.accounts.create({
      type: "express",
      country: "US",
      email: email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: "individual",
      individual: {
        first_name: firstName,
        last_name: lastName,
        email: email,
      },
      metadata: {
        user_id: userId,
        platform: "xosportshub",
      },
    });

    // Check if this is for onboarding or settings
    const isOnboarding = req.body.context === "onboarding";
    const baseUrl = isOnboarding
      ? "/stripe-onboarding-return"
      : "/seller/payment-settings";

    // Create account link for Express account onboarding
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${process.env.FRONTEND_URL
        }${baseUrl}?stripe_refresh=true&account_id=${account.id}${isOnboarding ? "&context=onboarding" : ""
        }`,
      return_url: `${process.env.FRONTEND_URL
        }${baseUrl}?stripe_success=true&account_id=${account.id}${isOnboarding ? "&context=onboarding" : ""
        }`,
      type: "account_onboarding",
      collect: "eventually_due", // Collect all required information
    });

    // Update user with Stripe Connect account ID (don't mark as complete yet)
    await User.findByIdAndUpdate(userId, {
      "paymentInfo.stripeConnectId": account.id,
    });

    res.status(200).json({
      success: true,
      data: {
        accountId: account.id,
        onboardingUrl: accountLink.url,
        accountType: "express",
        isExisting: false,
      },
    });
  } catch (err) {
    console.error("Stripe Connect account creation error:", err);
    next(new ErrorResponse("Failed to create Stripe Connect account", 500));
  }
};

// @desc    Get Stripe Connect account status
// @route   GET /api/payments/connect-account-status/:accountId
// @access  Private/Seller
exports.getConnectAccountStatus = async (req, res, next) => {
  try {
    const { accountId } = req.params;
    const userId = req.user.id;

    // Verify the account belongs to the user
    const user = await User.findById(userId);
    if (user.paymentInfo?.stripeConnectId !== accountId) {
      return next(
        new ErrorResponse("Unauthorized access to this account", 403)
      );
    }

    // Get account details from Stripe
    const account = await stripe.accounts.retrieve(accountId);

    res.status(200).json({
      success: true,
      data: {
        accountId: account.id,
        detailsSubmitted: account.details_submitted,
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
        requirements: account.requirements,
      },
    });
  } catch (err) {
    console.error("Stripe Connect account status error:", err);
    next(new ErrorResponse("Failed to get account status", 500));
  }
};

// @desc    Create Stripe dashboard link for Connect account
// @route   POST /api/payments/create-dashboard-link
// @access  Private/Seller
exports.createDashboardLink = async (req, res, next) => {
  try {
    const userId = req.user.id;

    // Get user's Stripe Connect account
    const user = await User.findById(userId);
    if (!user.paymentInfo?.stripeConnectId) {
      return next(new ErrorResponse("No Stripe Connect account found", 400));
    }

    const accountId = user.paymentInfo.stripeConnectId;

    // First, retrieve the account to check its type and status
    const account = await stripe.accounts.retrieve(accountId);

    // Check if account is properly set up
    if (!account.details_submitted) {
      return next(
        new ErrorResponse(
          "Account setup is not complete. Please complete your account setup first.",
          400
        )
      );
    }

    // For Express accounts, create a login link
    if (account.type === "express") {
      try {
        const loginLink = await stripe.accounts.createLoginLink(accountId);

        return res.status(200).json({
          success: true,
          data: {
            dashboardUrl: loginLink.url,
            accountType: "express",
          },
        });
      } catch (loginError) {
        // If login link creation fails, provide alternative access
        console.error("Login link creation failed:", loginError);

        // Return a direct link to Stripe Dashboard
        return res.status(200).json({
          success: true,
          data: {
            dashboardUrl: `https://dashboard.stripe.com/dashboard`,
            accountType: "express",
            message:
              "Please log in to your Stripe account to access the dashboard",
          },
        });
      }
    }

    // For Standard accounts (legacy support), create a login link
    if (account.type === "standard") {
      try {
        const loginLink = await stripe.accounts.createLoginLink(accountId);

        return res.status(200).json({
          success: true,
          data: {
            dashboardUrl: loginLink.url,
            accountType: "standard",
          },
        });
      } catch (loginError) {
        // If login link creation fails, provide alternative access
        console.error("Login link creation failed:", loginError);

        // Return a direct link to Stripe Dashboard
        return res.status(200).json({
          success: true,
          data: {
            dashboardUrl: `https://dashboard.stripe.com/dashboard`,
            accountType: "standard",
            message:
              "Please log in to your Stripe account to access the dashboard",
          },
        });
      }
    }

    // For custom accounts or other types
    return res.status(200).json({
      success: true,
      data: {
        dashboardUrl: `https://dashboard.stripe.com/dashboard`,
        accountType: account.type || "unknown",
        message: "Please log in to your Stripe account to access the dashboard",
      },
    });
  } catch (err) {
    console.error("Stripe dashboard link creation error:", err);

    // Provide a fallback response instead of just throwing an error
    if (err.type === "StripeInvalidRequestError") {
      return res.status(200).json({
        success: true,
        data: {
          dashboardUrl: `https://dashboard.stripe.com/dashboard`,
          accountType: "fallback",
          message:
            "Please log in to your Stripe account to access the dashboard",
        },
      });
    }

    next(new ErrorResponse("Failed to create dashboard link", 500));
  }
};

// @desc    Update Connect account with additional information
// @route   POST /api/payments/update-connect-account
// @access  Private/Seller
exports.updateConnectAccount = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { accountId, businessType, businessProfile } = req.body;

    // Verify the account belongs to the user
    const user = await User.findById(userId);
    if (user.paymentInfo?.stripeConnectId !== accountId) {
      return next(
        new ErrorResponse("Unauthorized access to this account", 403)
      );
    }

    // Update account with additional information for Express accounts
    const updateData = {};

    if (businessType) {
      updateData.business_type = businessType;
    }

    if (businessProfile) {
      updateData.business_profile = businessProfile;
    }

    // Update the account in Stripe
    const account = await stripe.accounts.update(accountId, updateData);

    res.status(200).json({
      success: true,
      data: {
        accountId: account.id,
        detailsSubmitted: account.details_submitted,
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
        requirements: account.requirements,
        capabilities: account.capabilities,
      },
    });
  } catch (err) {
    console.error("Stripe Connect account update error:", err);
    next(new ErrorResponse("Failed to update Stripe Connect account", 500));
  }
};

// @desc    Get detailed account information including capabilities
// @route   GET /api/payments/connect-account-details/:accountId
// @access  Private/Seller
exports.getConnectAccountDetails = async (req, res, next) => {
  try {
    const { accountId } = req.params;
    const userId = req.user.id;

    // Verify the account belongs to the user
    const user = await User.findById(userId);
    if (user.paymentInfo?.stripeConnectId !== accountId) {
      return next(
        new ErrorResponse("Unauthorized access to this account", 403)
      );
    }

    // Get detailed account information from Stripe
    const account = await stripe.accounts.retrieve(accountId);

    res.status(200).json({
      success: true,
      data: {
        accountId: account.id,
        accountType: account.type,
        email: account.email,
        detailsSubmitted: account.details_submitted,
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
        requirements: account.requirements,
        capabilities: account.capabilities,
        businessType: account.business_type,
        country: account.country,
        defaultCurrency: account.default_currency,
        individual: account.individual,
        businessProfile: account.business_profile,
        futureRequirements: account.future_requirements,
        currentlyDue: account.requirements?.currently_due || [],
        eventuallyDue: account.requirements?.eventually_due || [],
        pastDue: account.requirements?.past_due || [],
      },
    });
  } catch (err) {
    console.error("Stripe Connect account details error:", err);
    next(new ErrorResponse("Failed to get account details", 500));
  }
};

// @desc    Create payment intent for bid purchase
// @route   POST /api/payments/create-bid-payment-intent
// @access  Private/Buyer
exports.createBidPaymentIntent = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { bidId, amount } = req.body;
    const Bid = require("../models/Bid");

    // Get bid with content and seller information
    const bid = await Bid.findById(bidId).populate({
      path: "content",
      populate: {
        path: "seller",
        select: "paymentInfo firstName lastName",
      },
    });

    if (!bid) {
      return next(new ErrorResponse(`Bid not found with id of ${bidId}`, 404));
    }

    // Make sure user is the bidder
    if (bid.bidder.toString() !== req.user.id && req.user.role !== "admin") {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to pay for this bid`,
          403
        )
      );
    }

    // Check if bid is in "Won" status
    if (bid.status !== "Won") {
      return next(
        new ErrorResponse(
          `Bid must be in 'Won' status to proceed with payment`,
          400
        )
      );
    }

    // Check if seller has completed Stripe Connect onboarding
    const seller = bid.content.seller;
    if (!seller.paymentInfo?.stripeConnectId) {
      return next(
        new ErrorResponse(
          `Seller has not completed payment setup. Please contact seller to complete their payment onboarding.`,
          400
        )
      );
    }

    // Verify seller's Stripe Connect account is active
    try {
      const connectAccount = await stripe.accounts.retrieve(
        seller.paymentInfo.stripeConnectId
      );
      if (
        !connectAccount.details_submitted ||
        !connectAccount.charges_enabled
      ) {
        return next(
          new ErrorResponse(
            `Seller's payment account is not fully set up. Please contact seller.`,
            400
          )
        );
      }
    } catch (stripeError) {
      return next(
        new ErrorResponse(
          `Seller's payment account is invalid. Please contact seller.`,
          400
        )
      );
    }

    // Calculate fees using database settings
    const Setting = require("../models/Setting");
    const settings = await Setting.getSingleton();
    const bidAmount = parseFloat(amount);

    // Use the new fee calculation method for transparency
    const feeBreakdown = Setting.calculateFeeBreakdown(bidAmount, settings);
    const platformFeeAmount = Math.round(feeBreakdown.platformCommission * 100); // in cents
    const totalAmount = bidAmount; // Buyer pays exactly the bid amount, not bid amount + platform fee
    const transferAmount = Math.round(feeBreakdown.sellerEarningsBeforeStripeFees * 100); // Amount to transfer in cents

    // Get or create Stripe customer
    let stripeCustomerId = req.user.paymentInfo?.stripeCustomerId;

    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        email: req.user.email,
        name: `${req.user.firstName} ${req.user.lastName}`,
        metadata: {
          userId: req.user.id,
        },
      });

      stripeCustomerId = customer.id;

      // Update user with Stripe customer ID
      await User.findByIdAndUpdate(req.user.id, {
        "paymentInfo.stripeCustomerId": stripeCustomerId,
      });
    }

    // Create payment intent with Stripe Connect transfer
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(totalAmount * 100), // Total amount in cents
      currency: "usd",
      customer: stripeCustomerId,
      description: `Payment for bid on ${bid.content.title || "Digital Content"
        } - Bid #${bid._id}`,
      metadata: {
        bidId: bid._id.toString(),
        contentId: bid.content._id.toString(),
        buyerId: req.user.id,
        sellerId: bid.content.seller._id.toString(),
        orderType: "Auction",
        bidAmount: bidAmount.toString(),
        platformFee: (platformFeeAmount / 100).toString(),
        totalAmount: totalAmount.toString(),
        transferAmount: (transferAmount / 100).toString(),
        stripeFee: feeBreakdown.stripeFee.toString(),
        finalSellerEarnings: feeBreakdown.finalSellerEarnings.toString(),
        buyerPaysAmount: feeBreakdown.buyerPaysAmount.toString(),
      },
      // Stripe Connect configuration for automatic transfers
      transfer_data: {
        destination: seller.paymentInfo.stripeConnectId,
      },
      application_fee_amount: platformFeeAmount, // Platform commission
      // Required for some regulations
      shipping: {
        name: req.user.name || "Digital Content Buyer",
        address: {
          line1: "Digital Content Delivery",
          city: "Online",
          state: "Digital",
          postal_code: "000000",
          country: "US",
        },
      },
    });

    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret,
    });
  } catch (err) {
    console.error("Error creating bid payment intent:", err);
    next(err);
  }
};

// @desc    Complete bid purchase after successful payment
// @route   POST /api/payments/complete-bid-purchase
// @access  Private/Buyer
exports.completeBidPurchase = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { bidId, paymentIntentId } = req.body;
    const Bid = require("../models/Bid");
    const Order = require("../models/Order");

    // Get bid with content information
    const bid = await Bid.findById(bidId)
      .populate("content")
      .populate("content.seller", "firstName lastName");

    if (!bid) {
      return next(new ErrorResponse(`Bid not found with id of ${bidId}`, 404));
    }

    // Make sure user is the bidder
    if (bid.bidder.toString() !== req.user.id && req.user.role !== "admin") {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to complete this purchase`,
          403
        )
      );
    }

    // Verify payment intent with Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== "succeeded") {
      return next(new ErrorResponse("Payment was not successful", 400));
    }

    // Check if order already exists for this bid
    const existingOrder = await Order.findOne({ bidId: bid._id });
    if (existingOrder && existingOrder.paymentStatus === "Completed") {
      return next(
        new ErrorResponse("Purchase has already been completed", 400)
      );
    }

    // Extract card details from payment intent (default to true for bid payments)
    const cardDetails = await extractAndSaveCardDetails(
      paymentIntent,
      req.user.id,
      true
    );

    // Calculate amounts using database settings
    const Setting = require("../models/Setting");
    const settings = await Setting.getSingleton();
    const bidAmount = bid.amount;

    // Use the new fee calculation method for transparency
    const feeBreakdown = Setting.calculateFeeBreakdown(bidAmount, settings);
    const platformFee = feeBreakdown.platformCommission;
    const stripeFee = feeBreakdown.stripeFee;
    const sellerEarnings = feeBreakdown.finalSellerEarnings; // Final amount after all fees
    const totalAmount = bidAmount; // Buyer pays exactly the bid amount, not bid amount + platform fee

    // Create or update order
    let order;
    if (existingOrder) {
      // Update existing order
      order = await Order.findByIdAndUpdate(
        existingOrder._id,
        {
          paymentStatus: "Completed",
          status: "Completed",
          paymentIntentId,
          paymentMethod: "card",
          cardDetails: cardDetails
            ? {
              cardType: cardDetails.cardType,
              lastFourDigits: cardDetails.lastFourDigits,
            }
            : undefined,
        },
        { new: true }
      );
    } else {
      // Create new order
      order = await Order.create({
        buyer: bid.bidder,
        seller: bid.content.seller,
        content: bid.content._id,
        orderType: "Auction",
        amount: bidAmount,
        platformFee,
        sellerEarnings,
        totalAmount,
        bidId: bid._id,
        paymentStatus: "Completed",
        status: "Completed",
        paymentIntentId,
        paymentMethod: "card",
        cardDetails: cardDetails
          ? {
            cardType: cardDetails.cardType,
            lastFourDigits: cardDetails.lastFourDigits,
          }
          : undefined,
      });
    }

    // Create payment record
    const paymentData = {
      order: order._id,
      buyer: bid.bidder,
      seller: bid.content.seller,
      amount: bidAmount,
      platformFee,
      stripeProcessingFee: stripeFee, // Store Stripe fee for transparency
      sellerEarnings,
      totalAmount,
      paymentMethod: "card",
      paymentIntentId,
      status: "Completed",
      payoutStatus: "Pending",
    };

    // Add card details if available
    if (cardDetails) {
      paymentData.cardDetails = cardDetails;
    }

    const payment = await Payment.create(paymentData);

    // Mark content as sold and update auction status if applicable
    const Content = require("../models/Content");
    const content = await Content.findById(bid.content._id);

    if (content && content.saleType === "Auction") {
      await Content.findByIdAndUpdate(bid.content._id, {
        isSold: true,
        soldAt: new Date(),
        auctionStatus: "Ended",
        auctionEndedAt: new Date(),
      });
    }

    res.status(200).json({
      success: true,
      data: {
        order,
        payment,
        message:
          "Purchase completed successfully! You can now download your content.",
      },
    });
  } catch (err) {
    console.error("Error completing bid purchase:", err);
    next(err);
  }
};
