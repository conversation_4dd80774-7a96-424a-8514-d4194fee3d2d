/* Buyer Request Detail Styles */
.buyer-request-detail {
  width: 100%;
}

/* Back Button */
.back-btn {
  display: flex;
  align-items: center;
  gap: var(--border-radius-medium);
  padding: var(--extrasmallfont) var(--basefont);
  background: var(--light-gray);
  border: none;
  border-radius: var(--border-radius);
  color: var(--secondary-color);
  font-size: var(--basefont);
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: var(--primary-light-color);
  color: var(--primary-color);
}

/* Request Overview */
.request-overview {
  margin-bottom: var(--heading4);
}

.overview-header {
  margin-bottom: var(--heading5);
}

.request-title-section {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  margin-bottom: var(--extrasmallfont);
}

.request-title {
  margin: 0;
  font-size: var(--heading3);
  color: var(--secondary-color);
  font-weight: 700;
}

.request-meta {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  color: var(--gray);
  font-size: var(--basefont);
}

.request-id {
  font-family: monospace;
  background: var(--light-gray);
  padding: var(--extrasmallfont) var(--border-radius-medium);
  border-radius: var(--border-radius);
}

.request-date {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
}

/* Action Section */
.action-section {
  margin: var(--heading5) 0;
  padding: var(--basefont);
  background: var(--primary-light-color);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
}

.payment-btn {
  display: flex;
  align-items: center;
  gap: var(--border-radius-medium);
}

/* Overview Grid */
.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--heading5);
}

.overview-card {
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: var(--basefont);
  background: var(--primary-light-color);
  border-bottom: 1px solid var(--light-gray);
}

.card-icon {
  font-size: var(--mediumfont);
  color: var(--primary-color);
}

.card-header h3 {
  margin: 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  font-weight: 600;
}

.card-content {
  padding: var(--basefont);
}

/* Seller Details */
.seller-details {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.seller-name {
  font-size: var(--mediumfont);
  font-weight: 600;
  color: var(--secondary-color);
}

.seller-email {
  font-size: var(--basefont);
  color: var(--gray);
}

/* Budget Details */
.budget-details,
.request-details {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.budget-item,
.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.budget-item .label,
.detail-item .label {
  font-size: var(--basefont);
  color: var(--gray);
}

.budget-item .value,
.detail-item .value {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--secondary-color);
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: var(--extrasmallfont) var(--basefont);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  text-transform: capitalize;
}

.status-orange {
  background: var(--warning-light-color);
  color: var(--warning-color);
}

.status-green {
  background: var(--success-light-color);
  color: var(--success-color);
}

.status-red {
  background: var(--error-light-color);
  color: var(--error-color);
}

.status-blue {
  background: var(--info-light-color);
  color: var(--info-color);
}

.status-purple {
  background: #f3e8ff;
  color: #7c3aed;
}

.status-gray {
  background: var(--light-gray);
  color: var(--gray);
}

/* Request Sections */
.request-section {
  margin-bottom: var(--heading4);
  padding: var(--heading5);
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.section-title {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
  font-weight: 600;
}

.description-content {
  background: var(--light-gray);
  padding: var(--basefont);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
}

.description-content p {
  margin: 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  line-height: 1.6;
}

/* Response Content */
.response-content,
.delivery-content {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.response-status,
.delivery-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.response-badge,
.delivery-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: var(--extrasmallfont) var(--basefont);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
}

.response-badge.accepted,
.delivery-badge {
  background: var(--success-light-color);
  color: var(--success-color);
}

.response-badge.rejected {
  background: var(--error-light-color);
  color: var(--error-color);
}

.response-date,
.delivery-date {
  font-size: var(--smallfont);
  color: var(--gray);
}

.response-message,
.delivery-message {
  background: var(--light-gray);
  padding: var(--basefont);
  border-radius: var(--border-radius);
}

.response-message h4,
.delivery-message h4 {
  margin: 0 0 var(--extrasmallfont) 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
}

.response-message p,
.delivery-message p {
  margin: 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  line-height: 1.5;
}

.delivered-content {
  display: flex;
  justify-content: flex-start;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading2);
  text-align: center;
}

.empty-icon {
  font-size: var(--heading1);
  color: var(--light-gray);
  margin-bottom: var(--heading5);
}

.empty-state h3 {
  margin: 0 0 var(--extrasmallfont) 0;
  color: var(--secondary-color);
  font-size: var(--heading5);
}

.empty-state p {
  margin: 0 0 var(--heading5) 0;
  color: var(--gray);
  font-size: var(--basefont);
  max-width: 400px;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .overview-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .request-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }
  
  .request-title {
    font-size: var(--heading4);
  }
  
  .request-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }
  
  .budget-item,
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }
  
  .response-status,
  .delivery-status {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }
}
