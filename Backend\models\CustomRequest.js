const mongoose = require('mongoose');

const CustomRequestSchema = new mongoose.Schema({
  buyer: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  seller: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  relatedContent: {
    type: mongoose.Schema.ObjectId,
    ref: 'Content',
    required: true
  },
  title: {
    type: String,
    required: [true, 'Please add a title'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [2000, 'Description cannot be more than 2000 characters']
  },
  sport: {
    type: String,
    required: [true, 'Please specify the sport'],
    enum: [
      'Basketball',
      'Football',
      'Soccer',
      'Baseball',
      'Tennis',
      'Golf',
      'Swimming',
      'Volleyball',
      'Running',
      'Cycling',
      'Fitness',
      'Yoga',
      'Other'
    ]
  },
  contentType: {
    type: String,
    required: [true, 'Please specify the content type'],
    enum: ['Video', 'PDF', 'Audio', 'Image', 'Text', 'Document']
  },
  requestedDeliveryDate: {
    type: Date
  },
  budget: {
    type: Number,
    required: [true, 'Please add a budget'],
    min: [0, 'Budget cannot be negative']
  },
  status: {
    type: String,
    enum: ['Pending', 'Accepted', 'Rejected', 'In Progress', 'Content Submitted', 'Completed', 'Cancelled'],
    default: 'Pending'
  },
  sellerResponse: {
    accepted: {
      type: Boolean
    },
    paymentType: {
      type: String,
      enum: ['full', 'half'],
      default: 'full'
    },
    price: {
      type: Number,
      min: [0, 'Price cannot be negative']
    },
    estimatedDeliveryDate: {
      type: Date
    },
    message: {
      type: String,
      maxlength: [1000, 'Message cannot be more than 1000 characters']
    },
    responseDate: {
      type: Date
    }
  },
  paymentDetails: {
    totalAmount: {
      type: Number,
      min: [0, 'Total amount cannot be negative']
    },
    paidAmount: {
      type: Number,
      min: [0, 'Paid amount cannot be negative'],
      default: 0
    },
    remainingAmount: {
      type: Number,
      min: [0, 'Remaining amount cannot be negative'],
      default: 0
    },
    initialPaymentCompleted: {
      type: Boolean,
      default: false
    },
    finalPaymentCompleted: {
      type: Boolean,
      default: false
    },
    initialOrderId: {
      type: mongoose.Schema.ObjectId,
      ref: 'Order'
    },
    finalOrderId: {
      type: mongoose.Schema.ObjectId,
      ref: 'Order'
    }
  },
  contentSubmission: {
    isSubmitted: {
      type: Boolean,
      default: false
    },
    submittedAt: {
      type: Date
    },
    contentId: {
      type: mongoose.Schema.ObjectId,
      ref: 'Content'
    },
    submissionMessage: {
      type: String,
      maxlength: [500, 'Submission message cannot be more than 500 characters']
    }
  },
  remainingPaymentRequested: {
    type: Boolean,
    default: false
  },
  remainingPaymentRequestedAt: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
CustomRequestSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Indexes for efficient queries
CustomRequestSchema.index({ buyer: 1, status: 1 });
CustomRequestSchema.index({ seller: 1, status: 1 });
CustomRequestSchema.index({ relatedContent: 1 });
CustomRequestSchema.index({ status: 1, createdAt: -1 });

module.exports = mongoose.model('CustomRequest', CustomRequestSchema);
