import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate, useSearchParams } from "react-router-dom";
import { toast } from "react-toastify";
import {
  selectMyDownloads,
  selectMyCustomContent,
  selectLoading,
  selectErrors,
  fetchBuyerDownloads,
  fetchBuyerCustomContent,
  clearError,
  selectPaginationBySection,
} from "../../redux/slices/buyerDashboardSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import { TableRowSkeleton } from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import Pagination from "../../components/common/Pagination";
import {
  FaDownload,
  FaSync,
  FaFilePdf,
  FaFileAudio,
  FaFileImage,
  FaFile,
  FaFileVideo,
} from "react-icons/fa";
import { FiEye } from "react-icons/fi";
import Table from "../../components/common/Table";
import {
  getImageUrl,
  getProxyThumbnailUrl,
  getProxyUrlWithAuth,
  getPlaceholderImage
} from "../../utils/constants";
import ThumbnailImage from "../../components/common/ThumbnailImage";
import { generateFilename, formatFileSize } from "../../utils/downloadUtils";
import { formatStandardDate } from "../../utils/dateValidation";
import "../../styles/BuyerDownloads.css";

const BuyerDownloads = () => {
  const dispatch = useDispatch();
  const downloads = useSelector(selectMyDownloads);
  const customContent = useSelector(selectMyCustomContent);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);
  const [downloadingItems, setDownloadingItems] = useState(new Set());
  const pagination = useSelector((state) =>
    selectPaginationBySection(state, "downloads")
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Initialize activeTab based on URL parameter
  const [activeTab, setActiveTab] = useState(() => {
    const tabParam = searchParams.get('tab');
    return tabParam === 'custom' ? 'custom' : 'downloads';
  });

  // Fetch downloads on component mount or when page changes
  useEffect(() => {
    dispatch(
      fetchBuyerDownloads({
        page: currentPage,
        limit: itemsPerPage,
      })
    );
    dispatch(fetchBuyerCustomContent());
  }, [dispatch, currentPage, itemsPerPage]);

  // Handle URL parameter changes
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam === 'custom') {
      setActiveTab('custom');
    } else {
      setActiveTab('downloads');
    }
  }, [searchParams]);

  // Handle page change
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    window.scrollTo(0, 0); // Scroll to top when page changes
  };

  // Handle retry
  const handleRetry = () => {
    dispatch(clearError("downloads"));
    dispatch(fetchBuyerDownloads());
  };

  // Handle download - DISABLED FOR SECURITY
  const handleDownload = async (download) => {
    toast.error(
      "Download functionality has been disabled for security purposes"
    );
  };

  const getFileIcon = (fileType) => {
    switch (fileType?.toLowerCase()) {
      case "video":
        return <FaFileVideo className="file-icon video" />;
      case "pdf":
      case "document":
        return <FaFilePdf className="file-icon pdf" />;
      case "audio":
        return <FaFileAudio className="file-icon audio" />;
      case "image":
        return <FaFileImage className="file-icon image" />;
      default:
        return <FaFile className="file-icon default" />;
    }
  };

  const formatDuration = (seconds) => {
    if (!seconds) return "";
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, "0")}:${secs
        .toString()
        .padStart(2, "0")}`;
    }
    return `${minutes}:${secs.toString().padStart(2, "0")}`;
  };

  const columns = [
    {
      key: "no",
      label: "No.",
      className: "no",
    },
    {
      key: "orderId",
      label: "Order Id",
      className: "order-id",
    },
    {
      key: "content",
      label: "Content",
      className: "content",
    },
    {
      key: "date",
      label: "Purchase Date",
      className: "date",
    },
    {
      key: "amount",
      label: "Amount",
      className: "amount",
    },
    {
      key: "downloads",
      label: "Downloads",
      className: "downloads",
    },
    {
      key: "actions",
      label: "Actions",
      className: "actions",
    },
  ];

  const renderRow = (download, index) => {
    const isDownloading = downloadingItems.has(download._id);
    const purchaseDate = formatStandardDate(download.downloadDate);

    return [
      <td key="no" className="no">
        {index + 1}
      </td>,
      <td key="orderId" className="order-id">
        #{download.orderId?.slice(-8) || download._id?.slice(-8)}
      </td>,
      <td key="content" className="content">
        <div className="content-item">
          <div className="content-image">
            <ThumbnailImage
              contentId={download.content?._id}
              thumbnailUrl={download.thumbnailUrl}
              alt={download.title}
              style={{
                width: "40px",
                height: "40px",
                objectFit: "cover",
                borderRadius: "6px",
              }}
              placeholderWidth={40}
              placeholderHeight={40}
              placeholderText="No Image"
            />
          </div>
          <div className="content-info">
            <div className="content-title">{download.title}</div>
            <div className="content-coach">By {download.coach}</div>
            <div className="content-meta">
              <span className="file-type">{download.fileType}</span>
              {download.fileSize && (
                <span className="file-size">
                  {" "}
                  • {formatFileSize(download.fileSize)}
                </span>
              )}
              {download.duration && (
                <span className="duration">
                  {" "}
                  • {formatDuration(download.duration)}
                </span>
              )}
            </div>
          </div>
        </div>
      </td>,
      <td key="date" className="date">
        {purchaseDate}
      </td>,
      <td key="amount" className="amount">
        ${download.amount?.toFixed(2) || "0.00"}
      </td>,
      <td key="downloads" className="downloads">
        <span className="download-count">
          {download.downloadCount || 0} times
        </span>
      </td>,
      <td key="actions" className="actions">
        <button
          className="action-btn view-btn"
          onClick={() => navigate(`/buyer/download-details/${download._id}`)}
          title="View details"
        >
          <FiEye />
        </button>
      </td>,
    ];
  };
  return (
    <SectionWrapper
      title="My Content"
      icon={<FaDownload className="BuyerSidebar__icon" />}
    >
      {/* Tab Navigation */}
      <div className="content-tabs">
        <button
          className={`tab-btn ${activeTab === 'downloads' ? 'active' : ''}`}
          onClick={() => setActiveTab('downloads')}
        >
          <FaDownload />
          Regular Downloads ({downloads.length})
        </button>
        <button
          className={`tab-btn ${activeTab === 'custom' ? 'active' : ''}`}
          onClick={() => setActiveTab('custom')}
        >
          <FaFile />
          Custom Content ({customContent.length})
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === 'downloads' && (
        <>
          {errors.downloads ? (
            <ErrorDisplay
              error={errors.downloads}
              onRetry={handleRetry}
              title="Failed to load downloads"
            />
          ) : loading.downloads ? (
            <div className="loading-container">
              <TableRowSkeleton columns={7} />
              <TableRowSkeleton columns={7} />
              <TableRowSkeleton columns={7} />
            </div>
          ) : downloads.length > 0 ? (
            <>
              <Table
                columns={columns}
                data={downloads}
                renderRow={renderRow}
                className="BuyerDownloads__table"
                emptyMessage="You have no downloads yet."
              />
              {pagination && pagination.totalPages > 1 && (
                <div className="BuyerDownloads__pagination">
                  <Pagination
                    currentPage={pagination.currentPage}
                    totalPages={pagination.totalPages}
                    onPageChange={handlePageChange}
                  />
                </div>
              )}
            </>
          ) : (
            <div className="BuyerDownloads__empty">
              <h3>No downloads yet</h3>
              <p>
                Your purchased content will appear here once you make your first
                purchase.
              </p>
            </div>
          )}
        </>
      )}

      {activeTab === 'custom' && (
        <>
          {errors.customContent ? (
            <ErrorDisplay
              error={errors.customContent}
              onRetry={() => dispatch(fetchBuyerCustomContent())}
              title="Failed to load custom content"
            />
          ) : loading.customContent ? (
            <div className="loading-container">
              <TableRowSkeleton columns={5} />
              <TableRowSkeleton columns={5} />
              <TableRowSkeleton columns={5} />
            </div>
          ) : customContent.length > 0 ? (
            <div className="custom-content-grid">
              {customContent.map((content) => (
                <div key={content._id} className="custom-content-card">
                  <div className="content-thumbnail">
                    <ThumbnailImage
                      src={getProxyThumbnailUrl(content._id)}
                      alt={content.title}
                      fallback={getPlaceholderImage(content.contentType)}
                    />
                  </div>
                  <div className="content-info">
                    <h4 className="content-title">{content.title}</h4>
                    <p className="request-title">Request: {content.requestTitle}</p>
                    <div className="content-meta">
                      <span className="content-type">{content.contentType}</span>
                      <span className="completion-date">
                        Completed: {formatStandardDate(content.completedAt)}
                      </span>
                    </div>
                    <button
                      className="download-btn"
                      onClick={() => navigate(`/buyer/download-details/${content._id}`)}
                    >
                      <FaDownload />
                      Access Content
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="BuyerDownloads__empty">
              <h3>No custom content yet</h3>
              <p>
                Your custom content will appear here once sellers complete your custom requests.
              </p>
            </div>
          )}
        </>
      )}
    </SectionWrapper>
  );
};

export default BuyerDownloads;
