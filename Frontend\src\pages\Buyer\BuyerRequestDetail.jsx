import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";

import SectionWrapper from "../../components/common/SectionWrapper";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import "../../styles/BuyerRequestDetail.css";
import {
  FiArrowLeft,
  FiUser,
  FiCalendar,
  FiDollarSign,
  FiFileText,
  FiCheck,
  FiX,
  FiClock,
  FiEye
} from "react-icons/fi";
import { MdRequestPage } from "react-icons/md";
import { toast } from "react-toastify";
import api from "../../services/api";
import { formatStandardDate } from "../../utils/dateValidation";

const BuyerRequestDetail = () => {
  const { requestId } = useParams();
  const navigate = useNavigate();
  // Local state
  const [request, setRequest] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch request details
  useEffect(() => {
    const fetchRequestDetails = async () => {
      try {
        setLoading(true);
        const response = await api.get(`/requests/${requestId}`);
        setRequest(response.data.data);
      } catch (error) {
        console.error('Error fetching request details:', error);
        setError(error.response?.data?.message || 'Failed to load request details');
      } finally {
        setLoading(false);
      }
    };

    if (requestId) {
      fetchRequestDetails();
    }
  }, [requestId]);

  const handlePayment = async () => {
    try {
      console.log('Payment Details:', request.paymentDetails);

      // Check if there's an order to pay for
      let orderId = null;

      if (request.paymentDetails?.initialOrderId && !request.paymentDetails?.initialPaymentCompleted) {
        orderId = request.paymentDetails.initialOrderId;
        console.log('Using initial order ID:', orderId);
      } else if (request.paymentDetails?.finalOrderId && !request.paymentDetails?.finalPaymentCompleted) {
        orderId = request.paymentDetails.finalOrderId;
        console.log('Using final order ID:', orderId);
      }

      if (orderId) {
        console.log('Navigating to checkout with order ID:', orderId);
        navigate(`/checkout/${orderId}`);
      } else {
        console.log('No order ID found. Request status:', request.status);
        console.log('Payment details:', request.paymentDetails);

        // If request is accepted but no order exists, try to create one
        if (request.status === 'Accepted' && !request.paymentDetails?.initialOrderId) {
          toast.info('Creating payment order... Please wait.');
          try {
            // Call the backend to create the missing order
            const response = await api.post(`/api/requests/${request._id}/create-payment-order`);
            if (response.data.success) {
              const { orderId } = response.data.data;
              toast.success('Payment order created successfully!');
              navigate(`/checkout/${orderId}`);
            } else {
              throw new Error(response.data.message || 'Failed to create payment order');
            }
          } catch (createOrderError) {
            console.error('Error creating payment order:', createOrderError);
            toast.error(createOrderError.response?.data?.message || 'Unable to create payment order. Please contact support.');
          }
        } else {
          toast.error('No payment is required at this time. Please wait for the seller to accept your request.');
        }
      }
    } catch (error) {
      console.error('Error navigating to payment:', error);
      toast.error('Unable to process payment at this time');
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'Pending': { color: 'orange', icon: <FiClock /> },
      'Accepted': { color: 'green', icon: <FiCheck /> },
      'Rejected': { color: 'red', icon: <FiX /> },
      'In Progress': { color: 'blue', icon: <FiClock /> },
      'Content Submitted': { color: 'purple', icon: <FiCheck /> },
      'Completed': { color: 'green', icon: <FiCheck /> }
    };

    const config = statusConfig[status] || { color: 'gray', icon: <FiClock /> };
    
    return (
      <span className={`status-badge status-${config.color}`}>
        {config.icon}
        {status}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="buyer-request-detail">
        <SectionWrapper
          icon={<MdRequestPage />}
          title="Request Details"
        >
          <LoadingSkeleton count={8} height="60px" />
        </SectionWrapper>
      </div>
    );
  }

  if (error) {
    return (
      <div className="buyer-request-detail">
        <SectionWrapper
          icon={<MdRequestPage />}
          title="Request Details"
        >
          <ErrorDisplay
            error={error}
            onRetry={() => window.location.reload()}
            title="Failed to load request details"
          />
        </SectionWrapper>
      </div>
    );
  }

  if (!request) {
    return (
      <div className="buyer-request-detail">
        <SectionWrapper
          icon={<MdRequestPage />}
          title="Request Details"
        >
          <div className="empty-state">
            <MdRequestPage className="empty-icon" />
            <h3>Request Not Found</h3>
            <p>The requested custom request could not be found.</p>
            <button 
              className="btn-primary"
              onClick={() => navigate('/buyer/account/requests')}
            >
              Back to Requests
            </button>
          </div>
        </SectionWrapper>
      </div>
    );
  }

  // Check if payment can be made (only after request exists)
  const canMakePayment = request && (
    (request.status === 'Accepted' &&
     request.paymentDetails?.initialOrderId &&
     !request.paymentDetails?.initialPaymentCompleted) ||
    (request.remainingPaymentRequested &&
     request.paymentDetails?.finalOrderId &&
     !request.paymentDetails?.finalPaymentCompleted)
  );

  return (
    <div className="buyer-request-detail">
      <SectionWrapper
        icon={<MdRequestPage />}
        title="Request Details"
        action={
          <button 
            className="back-btn"
            onClick={() => navigate('/buyer/account/requests')}
          >
            <FiArrowLeft />
            Back to Requests
          </button>
        }
      >
        {/* Request Overview */}
        <div className="request-overview">
          <div className="overview-header">
            <div className="request-title-section">
              <h1 className="request-title">{request.title}</h1>
              {getStatusBadge(request.status)}
            </div>
            <div className="request-meta">
              <span className="request-id">Request #{request._id.slice(-8)}</span>
              <span className="request-date">
                <FiCalendar />
                {formatStandardDate(request.createdAt)}
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          {canMakePayment && (
            <div className="action-section">
              <button
                className="btn-primary payment-btn"
                onClick={handlePayment}
              >
                <FiDollarSign />
                {request.remainingPaymentRequested ? 'Pay Remaining Amount' : 'Make Payment'}
              </button>
              <button
                className="btn-secondary refresh-btn"
                onClick={fetchRequestDetails}
                style={{ marginLeft: '10px' }}
              >
                🔄 Refresh
              </button>
            </div>
          )}

          <div className="overview-grid">
            <div className="overview-card">
              <div className="card-header">
                <FiUser className="card-icon" />
                <h3>Seller Information</h3>
              </div>
              <div className="card-content">
                <div className="seller-details">
                  <div className="seller-name">
                    {request.seller?.firstName} {request.seller?.lastName}
                  </div>
                  <div className="seller-email">{request.seller?.email}</div>
                </div>
              </div>
            </div>

            <div className="overview-card">
              <div className="card-header">
                <FiDollarSign className="card-icon" />
                <h3>Budget & Payment</h3>
              </div>
              <div className="card-content">
                <div className="budget-details">
                  <div className="budget-item">
                    <span className="label">Your Budget:</span>
                    <span className="value">${request.budget}</span>
                  </div>
                  {request.sellerResponse?.price && (
                    <div className="budget-item">
                      <span className="label">Seller Price:</span>
                      <span className="value">${request.sellerResponse.price}</span>
                    </div>
                  )}
                  {request.sellerResponse?.paymentType && (
                    <div className="budget-item">
                      <span className="label">Payment Type:</span>
                      <span className="value">
                        {request.sellerResponse.paymentType === 'half' ? 'Half Payment' : 'Full Payment'}
                      </span>
                    </div>
                  )}
                  {request.paymentDetails && (
                    <>
                      <div className="budget-item">
                        <span className="label">Total Amount:</span>
                        <span className="value">${request.paymentDetails.totalAmount}</span>
                      </div>
                      <div className="budget-item">
                        <span className="label">Paid Amount:</span>
                        <span className="value">${request.paymentDetails.paidAmount}</span>
                      </div>
                      {request.paymentDetails.remainingAmount > 0 && (
                        <div className="budget-item">
                          <span className="label">Remaining Amount:</span>
                          <span className="value">${request.paymentDetails.remainingAmount}</span>
                        </div>
                      )}
                      <div className="budget-item">
                        <span className="label">Initial Payment:</span>
                        <span className={`value ${request.paymentDetails.initialPaymentCompleted ? 'completed' : 'pending'}`}>
                          {request.paymentDetails.initialPaymentCompleted ? 'Completed' : 'Pending'}
                        </span>
                      </div>
                      {request.sellerResponse?.paymentType === 'half' && (
                        <div className="budget-item">
                          <span className="label">Final Payment:</span>
                          <span className={`value ${request.paymentDetails.finalPaymentCompleted ? 'completed' : 'pending'}`}>
                            {request.paymentDetails.finalPaymentCompleted ? 'Completed' : 'Pending'}
                          </span>
                        </div>
                      )}
                    </>
                  )}
                  {request.paymentDetails && (
                    <>
                      <div className="budget-item">
                        <span className="label">Paid Amount:</span>
                        <span className="value">${request.paymentDetails.paidAmount}</span>
                      </div>
                      {request.paymentDetails.remainingAmount > 0 && (
                        <div className="budget-item">
                          <span className="label">Remaining:</span>
                          <span className="value">${request.paymentDetails.remainingAmount}</span>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="overview-card">
              <div className="card-header">
                <FiFileText className="card-icon" />
                <h3>Request Details</h3>
              </div>
              <div className="card-content">
                <div className="request-details">
                  <div className="detail-item">
                    <span className="label">Content Type:</span>
                    <span className="value">{request.contentType}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Sport:</span>
                    <span className="value">{request.sport}</span>
                  </div>
                  {request.requestedDeliveryDate && (
                    <div className="detail-item">
                      <span className="label">Requested Delivery:</span>
                      <span className="value">{formatStandardDate(request.requestedDeliveryDate)}</span>
                    </div>
                  )}
                  {request.sellerResponse?.estimatedDeliveryDate && (
                    <div className="detail-item">
                      <span className="label">Estimated Delivery:</span>
                      <span className="value">{formatStandardDate(request.sellerResponse.estimatedDeliveryDate)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Request Description */}
        <div className="request-section">
          <h2 className="section-title">Your Request</h2>
          <div className="description-content">
            <p>{request.description}</p>
          </div>
        </div>

        {/* Seller Response Section */}
        {request.sellerResponse && (
          <div className="request-section">
            <h2 className="section-title">Seller Response</h2>
            <div className="response-content">
              <div className="response-status">
                <span className={`response-badge ${request.sellerResponse.accepted ? 'accepted' : 'rejected'}`}>
                  {request.sellerResponse.accepted ? <FiCheck /> : <FiX />}
                  {request.sellerResponse.accepted ? 'Accepted' : 'Rejected'}
                </span>
                <span className="response-date">
                  {formatStandardDate(request.sellerResponse.responseDate)}
                </span>
              </div>
              {request.sellerResponse.message && (
                <div className="response-message">
                  <h4>Seller Message:</h4>
                  <p>{request.sellerResponse.message}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Content Delivery Section */}
        {request.contentSubmission?.isSubmitted && (
          <div className="request-section">
            <h2 className="section-title">Content Delivered</h2>
            <div className="delivery-content">
              <div className="delivery-status">
                <span className="delivery-badge">
                  <FiCheck />
                  Content Delivered
                </span>
                <span className="delivery-date">
                  {formatStandardDate(request.contentSubmission.submittedAt)}
                </span>
              </div>
              {request.contentSubmission.submissionMessage && (
                <div className="delivery-message">
                  <h4>Delivery Message:</h4>
                  <p>{request.contentSubmission.submissionMessage}</p>
                </div>
              )}
              {request.contentSubmission.contentId && (
                <div className="delivered-content">
                  <button 
                    className="btn-primary"
                    onClick={() => navigate(`/buyer/content/${request.contentSubmission.contentId}`)}
                  >
                    <FiEye />
                    View Your Content
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </SectionWrapper>
    </div>
  );
};

export default BuyerRequestDetail;
